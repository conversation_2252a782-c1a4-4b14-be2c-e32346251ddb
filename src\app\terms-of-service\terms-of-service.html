<div class="min-h-screen bg-white">

  <!-- Header consistent sa design -->
  <header class="bg-gray-950 shadow-xl border-b-4 border-orange-500 relative z-10">
    <div class="px-4 sm:px-6 py-3 sm:py-4 flex justify-between items-center">
      <div class="flex-1 flex justify-start items-center">
        <a routerLink="/" class="hover:opacity-80 transition duration-300 flex items-center">
          <img
            src="assets/images/BcLogo.png"
            alt="Benedicto College Logo"
            class="h-10 sm:h-14 md:h-16 lg:h-20 w-auto max-w-full object-contain"
          >
        </a>
      </div>
      <nav class="flex-shrink-0">
        <button
          routerLink="/login"
          class="bg-black hover:bg-gray-800 text-white font-semibold py-2 px-4 sm:px-6 rounded-lg transition duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 text-sm sm:text-base flex items-center space-x-2"
        >
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
          </svg>
          <span>Back to Login</span>
        </button>
      </nav>
    </div>
  </header>

  <!-- Main content section para sa terms of service -->
  <main class="py-12 px-6 sm:px-8 lg:px-12">
      <!-- Terms of Service Content -->
        
        <!-- Header section -->
        <div class="mb-12">
          <h1 class="text-3xl font-bold text-gray-900 mb-4">Terms of Service</h1>
          <p class="text-lg text-gray-600 max-w-4xl">
            By accessing and using the Benedicto College Library Management System, you agree to comply with these terms and conditions.
          </p>
        </div>

        <!-- Effective Date -->
        <div class="border-l-4 border-green-500 pl-6 py-4 mb-8">
          <p class="font-semibold text-gray-900">Effective Date: January 1, 2025</p>
        </div>

        <!-- Terms Content -->
        <div class="space-y-10">
          
          <!-- Acceptance of Terms -->
          <section class="border-l-4 border-blue-500 pl-6 py-4">
            <h2 class="text-2xl font-semibold text-gray-900 mb-4">1. Acceptance of Terms</h2>
            <div class="space-y-4">
              <p class="text-gray-700 leading-relaxed">
                By creating an account and accessing the Benedicto College Library Management System, you acknowledge that you have read, understood, and agree to be bound by these Terms of Service and our Privacy Policy.
              </p>
              <p class="text-gray-700 leading-relaxed">
                These terms constitute a legally binding agreement between you and Benedicto College. If you do not agree to these terms, you must not use our library services.
              </p>
            </div>
          </section>

          <!-- User Eligibility -->
          <section class="border-l-4 border-green-500 pl-6 py-4">
            <h2 class="text-2xl font-semibold text-gray-900 mb-4">2. User Eligibility</h2>
            <div class="space-y-4">
              <h3 class="text-lg font-medium text-gray-800">Authorized Users:</h3>
              <ul class="list-disc list-inside space-y-2 text-gray-700 ml-4">
                <li>Currently enrolled students of Benedicto College</li>
                <li>Faculty and staff members with valid institutional credentials</li>
                <li>Authorized researchers and visiting scholars</li>
                <li>Alumni with active library privileges</li>
              </ul>

              <div class="border-l-4 border-red-500 pl-4 py-2 mt-6">
                <p class="text-red-800 font-medium">
                  Unauthorized access or sharing of credentials is strictly prohibited and may result in account suspension and disciplinary action.
                </p>
              </div>
            </div>
          </section>

          <!-- Acceptable Use Policy -->
          <section class="border-l-4 border-purple-500 pl-6 py-4">
            <h2 class="text-2xl font-semibold text-gray-900 mb-4">3. Acceptable Use Policy</h2>
            <div class="space-y-6">
              <div>
                <h3 class="text-lg font-medium text-gray-800 mb-3">Permitted Activities:</h3>
                <ul class="list-disc list-inside space-y-2 text-gray-700 ml-4">
                  <li>Academic research and educational purposes</li>
                  <li>Borrowing and returning library materials</li>
                  <li>Accessing digital resources and databases</li>
                  <li>Using study spaces and computer facilities</li>
                  <li>Participating in library programs and events</li>
                </ul>
              </div>

              <div>
                <h3 class="text-lg font-medium text-gray-800 mb-3">Prohibited Activities:</h3>
                <ul class="list-disc list-inside space-y-2 text-gray-700 ml-4">
                  <li>Commercial use of library resources</li>
                  <li>Sharing login credentials with unauthorized persons</li>
                  <li>Downloading or distributing copyrighted materials illegally</li>
                  <li>Attempting to bypass security measures</li>
                  <li>Using the system for illegal or unethical purposes</li>
                  <li>Disrupting library operations or other users</li>
                </ul>
              </div>
            </div>
          </section>

          <!-- Account Responsibilities -->
          <section class="border-l-4 border-orange-500 pl-6 py-4">
            <h2 class="text-2xl font-semibold text-gray-900 mb-4">4. Account Responsibilities</h2>
            <div class="space-y-4">
              <p class="text-gray-700 leading-relaxed">
                You are responsible for maintaining the confidentiality of your account credentials and for all activities that occur under your account.
              </p>
              <h3 class="text-lg font-medium text-gray-800 mt-6 mb-3">Your Responsibilities Include:</h3>
              <ul class="list-disc list-inside space-y-2 text-gray-700 ml-4">
                <li>Keeping your password secure and not sharing it with others</li>
                <li>Notifying the library immediately of any unauthorized use</li>
                <li>Ensuring your contact information is current and accurate</li>
                <li>Returning borrowed materials on time</li>
                <li>Paying any applicable fines or fees promptly</li>
              </ul>
            </div>
          </section>

          <!-- Intellectual Property -->
          <section class="border-l-4 border-indigo-500 pl-6 py-4">
            <h2 class="text-2xl font-semibold text-gray-900 mb-4">5. Intellectual Property Rights</h2>
            <div class="space-y-4">
              <p class="text-gray-700 leading-relaxed">
                All content, materials, and resources provided through the library system are protected by copyright and other intellectual property laws.
              </p>
              <p class="text-gray-700 leading-relaxed">
                Users must respect copyright restrictions and licensing agreements. Fair use provisions apply to academic and research activities as defined by applicable copyright law.
              </p>
            </div>
          </section>

          <!-- Limitation of Liability -->
          <section class="border-l-4 border-red-500 pl-6 py-4">
            <h2 class="text-2xl font-semibold text-gray-900 mb-4">6. Limitation of Liability</h2>
            <div class="space-y-4">
              <p class="text-gray-700 leading-relaxed">
                Benedicto College provides library services "as is" and makes no warranties regarding system availability, accuracy of information, or fitness for particular purposes.
              </p>
              <p class="text-gray-700 leading-relaxed">
                The college shall not be liable for any indirect, incidental, or consequential damages arising from the use of library services.
              </p>
            </div>
          </section>

        </div>

        <!-- Contact Information -->
        <div class="mt-12 border-l-4 border-gray-500 pl-6 py-4">
          <h3 class="text-xl font-semibold text-gray-900 mb-4">Questions or Concerns?</h3>
          <p class="text-gray-700 mb-6">
            If you have questions about these Terms of Service or need clarification on any policies, please contact our library administration:
          </p>
          <div class="space-y-3">
            <p class="text-gray-700"><strong>Email:</strong> legal&#64;benedictocollege.edu.ph</p>
            <p class="text-gray-700"><strong>Phone:</strong> (*************</p>
            <p class="text-gray-700"><strong>Office:</strong> Library Administration Building</p>
            <p class="text-gray-700"><strong>Hours:</strong> Monday-Friday, 8:00 AM - 5:00 PM</p>
          </div>
        </div>
  </main>

  <!-- Footer -->
  <footer class="bg-black py-12 border-t-8 border-orange-500">
    <div class="px-6">
      <!-- Mobile/Tablet: Grid Layout (below 1000px) -->
      <div class="block xl:hidden">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-8 mb-8">
          <!-- Get in Touch Section -->
          <div class="text-center md:text-left">
            <h3 class="text-xl font-bold text-white mb-4">Contact Us</h3>
            <div class="space-y-3 text-gray-300">
              <div class="flex items-center justify-center md:justify-start">
                <svg class="w-5 h-5 text-green-400 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                </svg>
                <span>(*************</span>
              </div>
              <div class="flex items-center justify-center md:justify-start">
                <svg class="w-5 h-5 text-green-400 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                </svg>
                <span>Benedicto College Library</span>
              </div>
            </div>
          </div>

          <!-- Quick Links Section -->
          <div class="text-center md:text-left">
            <h3 class="text-xl font-bold text-white mb-4">Quick Links</h3>
            <div class="space-y-2">
              <a routerLink="/about" class="block text-gray-400 hover:text-green-400 transition duration-300">About Us</a>
              <a routerLink="/contact" class="block text-gray-400 hover:text-green-400 transition duration-300">Contact</a>
            </div>
          </div>

          <!-- Connect with Us Section -->
          <div class="text-center md:text-left">
            <h3 class="text-xl font-bold text-white mb-4">Connect with Us</h3>
            <div class="flex justify-center md:justify-start space-x-4">
              <a href="https://facebook.com/benedictocollege" target="_blank" class="bg-gray-800 hover:bg-green-500 p-3 rounded-full transition duration-300 transform hover:scale-110">
                <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                </svg>
              </a>
              <a href="https://benedictocollege.edu.ph" target="_blank" class="bg-gray-800 hover:bg-green-500 p-3 rounded-full transition duration-300 transform hover:scale-110">
                <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9v-9m0-9v9m0 9c-5 0-9-4-9-9s4-9 9-9"></path>
                </svg>
              </a>
              <a href="tel:+63321234567" class="bg-gray-800 hover:bg-green-500 p-3 rounded-full transition duration-300 transform hover:scale-110">
                <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                </svg>
              </a>
            </div>
          </div>
        </div>
      </div>

      <!-- Desktop: Flexbox Layout (1000px and beyond) -->
      <div class="hidden xl:block">
        <div class="flex justify-between items-start mb-8">
          <!-- Get in Touch Section -->
          <div class="flex-1">
            <h3 class="text-xl font-bold text-white mb-4">Get in Touch</h3>
            <div class="space-y-3 text-gray-300">
              <div class="flex items-center">
                <svg class="w-5 h-5 text-green-400 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                </svg>
                <span>(*************</span>
              </div>
              <div class="flex items-center">
                <svg class="w-5 h-5 text-green-400 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                </svg>
                <span>Benedicto College Library</span>
              </div>
            </div>
          </div>

          <!-- Quick Links Section -->
          <div class="flex-1">
            <h3 class="text-xl font-bold text-white mb-4">Quick Links</h3>
            <div class="space-y-2">
              <a routerLink="/about" class="block text-gray-400 hover:text-green-400 transition duration-300">About Us</a>
              <a routerLink="/contact" class="block text-gray-400 hover:text-green-400 transition duration-300">Contact</a>
            </div>
          </div>

          <!-- Connect with Us Section -->
          <div class="flex-1">
            <h3 class="text-xl font-bold text-white mb-4">Connect with Us</h3>
            <div class="flex space-x-4">
              <a href="https://facebook.com/benedictocollege" target="_blank" class="bg-gray-800 hover:bg-green-500 p-3 rounded-full transition duration-300 transform hover:scale-110">
                <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                </svg>
              </a>
              <a href="https://benedictocollege.edu.ph" target="_blank" class="bg-gray-800 hover:bg-green-500 p-3 rounded-full transition duration-300 transform hover:scale-110">
                <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9v-9m0-9v9m0 9c-5 0-9-4-9-9s4-9 9-9"></path>
                </svg>
              </a>
              <a href="tel:+63321234567" class="bg-gray-800 hover:bg-green-500 p-3 rounded-full transition duration-300 transform hover:scale-110">
                <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                </svg>
              </a>
            </div>
          </div>
        </div>
      </div>

      <!-- Copyright Section -->
      <div class="border-t border-gray-700 pt-6">
        <div class="flex flex-col xl:flex-row justify-between items-center">
          <div class="text-gray-400 mb-4 xl:mb-0 text-center xl:text-left">
            &copy; 2025 Benedicto College Library Management System. All Rights Reserved.
          </div>
          <div class="text-gray-400 text-sm text-center xl:text-right">
            Your Education… Our Mission
          </div>
        </div>
      </div>
    </div>
  </footer>

  <!-- Scroll to Top Button -->
  <button
    id="scrollToTopBtn"
    onclick="scrollToTop()"
    class="fixed bottom-6 right-6 bg-orange-500 hover:bg-orange-600 text-white p-3 rounded-full shadow-lg transition-all duration-300 transform hover:scale-110 z-50 opacity-0 invisible"
    title="Scroll to top"
  >
    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 15l7-7 7 7"></path>
    </svg>
  </button>
</div>

<script>
  // Scroll to Top functionality
  function scrollToTop() {
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    });
  }

  // Show/hide scroll to top button based on scroll position
  window.addEventListener('scroll', function() {
    const scrollToTopBtn = document.getElementById('scrollToTopBtn');
    if (window.pageYOffset > 300) {
      scrollToTopBtn.classList.remove('opacity-0', 'invisible');
      scrollToTopBtn.classList.add('opacity-100', 'visible');
    } else {
      scrollToTopBtn.classList.remove('opacity-100', 'visible');
      scrollToTopBtn.classList.add('opacity-0', 'invisible');
    }
  });
</script>
